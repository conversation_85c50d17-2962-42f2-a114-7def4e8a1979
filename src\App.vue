<script setup>
import { computed } from 'vue'
import NavBar from '@/components/NavBar.vue'
import TabBar from '@/components/TabBar.vue'

const mode = computed(() => {
  return 'light'
})
</script>

<template>
  <van-config-provider :theme="mode">
    <nav-bar />
    <router-view v-slot="{ Component }">
      <section class="app-wrapper">
        <keep-alive>
          <component :is="Component" />
        </keep-alive>
      </section>
    </router-view>
    <tab-bar />
  </van-config-provider>
</template>

<style scoped>
.app-wrapper {
  width: 100%;
  position: relative;
  padding-top: 46px; /* 为顶部导航栏留出空间 */
  padding-bottom: 50px; /* 为底部导航栏留出空间 */
  min-height: 100vh;
  box-sizing: border-box;
}
</style>

