<!-- 公共导航栏 -->
<script setup>
import { computed } from 'vue'
import { useRoute,useRouter } from 'vue-router'
const route = useRoute()
const router = useRouter()

function onBack() {
  if (window.history.state.back)
    history.back()
  else
    router.replace('/')
}


const title = computed(() => {
  console.log(route)
  if (!route.meta)
    return ''

  return route.meta.title
})

const showLeftArrow = computed(() => route.name !== 'Home')
</script>

<template>
  <VanNavBar
    :title="title"
    :fixed="true"
    clickable placeholder
    :left-arrow="showLeftArrow"
    @click-left="onBack"
  />
</template>
