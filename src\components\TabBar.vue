<!-- 底部导航栏 -->
<script setup>
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

const active = computed({
  get() {
    return route.name
  },
  set(value) {
    router.push({ name: value })
  }
})

const tabs = [
  {
    name: 'Home',
    title: '首页',
    icon: 'home-o'
  },
  {
    name: 'Mine',
    title: '我的',
    icon: 'user-o'
  }
]
</script>

<template>
  <VanTabbar v-model="active" fixed>
    <VanTabbarItem
      v-for="tab in tabs"
      :key="tab.name"
      :name="tab.name"
      :icon="tab.icon"
    >
      {{ tab.title }}
    </VanTabbarItem>
  </VanTabbar>
</template>
