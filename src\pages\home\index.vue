<template>
  <div class="page">
    <div class="wrap1">
      <div class="container1">
        <div class="content1">
          <div class="main2">
            <div class="section">
              <div class="subSection"></div>
              <p class="text1"><span class="text1__seg0">9:41</span></p>
            </div>
            <div class="section1">
              <div class="subSection1">
                <img
                  :src="('./assets/Image1.png')"
                  class="image1"
                  alt="Image Asset 1"
                /><img
                  :src="('./assets/Image2.png')"
                  class="image2"
                  alt="Image Asset 2"
                />
                <div class="block">
                  <div class="subBlock">
                    <div class="div"></div>
                    <img
                      :src="('./assets/Image3.png')"
                      class="image3"
                      alt="Image Asset 3"
                    />
                  </div>
                  <div class="subBlock1"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="content2">
          <div class="main3">
            <div class="section2">
              <div class="subSection2">
                <img
                  :src="('./assets/Image4.png')"
                  class="image4"
                  alt="Image Asset 4"
                />
              </div>
            </div>
            <p class="text2">待开会议</p>
          </div>
        </div>
      </div>
    </div>
    <div class="wrap">
      <div class="container">
        <div class="content">
          <div class="main">
            <img
              :src="('./assets/Image5.png')"
              class="image"
              alt="Image Asset 5"
            />
          </div>
          <div class="main1"><p class="text">输入会议名称查询</p></div>
        </div>
      </div>
    </div>
    <div class="wrap2">
      <div class="container2">
        <div class="content3">
          <div class="main4">
            <p class="text3">紧急技术评审</p>
            <div class="section3">
              <div class="subSection3">
                <p class="text4">开始时间</p>
                <p class="text5">2025-06-30&nbsp;18:58:51</p>
              </div>
            </div>
            <div class="section4">
              <div class="subSection4">
                <p class="text6">会议地点</p>
                <p class="text7">成都市农业农村局1006会议室</p>
              </div>
            </div>
            <div class="section5">
              <div class="subSection5">
                <p class="text8">会议议题</p>
                <p class="text9">
                  1、关于高标准农田建设的技术方案汇报；2、关于技术方案的头脑风暴讨论；3、关于建设周.
                </p>
              </div>
            </div>
            <div class="section6"><p class="text10">查看详情</p></div>
          </div>
        </div>
        <div class="content4">
          <div class="main5">
            <p class="text11">紧急技术评审</p>
            <div class="section7">
              <div class="subSection6">
                <p class="text12">开始时间</p>
                <p class="text13">2025-06-30&nbsp;18:58:51</p>
              </div>
            </div>
            <div class="section8">
              <div class="subSection7">
                <p class="text14">会议地点</p>
                <p class="text15">
                  成都市农业农村局1006会议室成都市农业农村局1006会议室
                </p>
              </div>
            </div>
            <div class="section9">
              <div class="subSection8">
                <p class="text16">会议议题</p>
                <p class="text17">
                  1、关于高标准农田建设的技术方案汇报；2、关于技术方案的头脑风暴讨论；3、关于建设周.
                </p>
              </div>
            </div>
            <div class="section10"><p class="text18">查看详情</p></div>
          </div>
        </div>
        <p class="text19">没有更多了</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'GeneratedComponent',

  props: {

  },
  setup(props) {


    return {


    };
  },
};
</script>

<style scoped>
.page {
	width: 375px;
	height: 907px;
	overflow: hidden;
	box-sizing: border-box;
	background: rgba(243,245,246,1);
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	align-items: center;
}
.wrap1 {
	width: 375px;
	height: 88px;
	box-sizing: border-box;
	background: rgba(255,255,255,1);
	display: flex;
	flex-direction: column;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
}
.container1 {
	width: 375px;
	height: 88px;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	align-items: center;
}
.content1 {
	width: 375px;
	height: 44px;
	overflow: hidden;
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 250px;
	padding-right: 14px;
	padding-left: 14px;
}
.main2 {
	width: 347px;
	height: 19px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.section {
	width: 59.62px;
	height: 19px;
	overflow: hidden;
	box-sizing: border-box;
	position: relative;
}
.subSection {
	width: 59.62px;
	box-sizing: border-box;
	min-height: 19px;
	position: absolute;
	top: 0px;
	left: 0px;
}
.text1 {
	position: absolute;
	top: 0.33px;
	left: 0px;
	width: 59.62px;
	font-family: 'PingFang SC';
	font-size: 15px;
	white-space: nowrap;
	color: rgba(0,0,0,1);
	line-height: 21px;
	letter-spacing: -0.30px;
	font-weight: 500;
}
.text1__seg0 {
	letter-spacing: -0.30px;
}
.section1 {
	width: 67.33px;
	height: 12px;
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 5px;
}
.subSection1 {
	width: 67.33px;
	height: 12px;
	display: flex;
	justify-content: flex-start;
	align-items: flex-start;
}
.image1 {
	width: 17px;
	height: 10.67px;
	margin-top: 0.67px;
}
.image2 {
	width: 15.33px;
	height: 11px;
	margin-top: 0.50px;
	margin-left: 5px;
}
.block {
	margin-left: 5px;
	position: relative;
	width: 25px;
	height: 12px;
}
.subBlock {
	width: 25px;
	height: 12px;
	box-sizing: border-box;
	position: absolute;
	top: 0px;
	left: 0px;
	display: flex;
	justify-content: flex-start;
	align-items: flex-start;
}
.div {
	width: 18px;
	background-color: rgba(0,0,0,1);
	border-radius: 1.33px;
	box-sizing: border-box;
	background: rgba(0,0,0,1);
	min-height: 7.33px;
	margin-top: 2.33px;
	margin-left: 2.33px;
}
.image3 {
	width: 1.33px;
	height: 4px;
	margin-top: 4px;
	margin-left: 3px;
}
.subBlock1 {
	width: 22px;
	mix-blend-mode: normal;
	opacity: 0.35;
	border-radius: 2.67px;
	border-color: rgba(0,0,0,1);
	border-style: solid;
	border-width: 1px;
	box-sizing: border-box;
	min-height: 11.33px;
	position: absolute;
	top: 0.33px;
	left: 0.33px;
}
.content2 {
	width: 375px;
	height: 44px;
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: flex-start;
	align-items: center;
	gap: 114px;
}
.main3 {
	width: 221.50px;
	height: 44px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.section2 {
	width: 40px;
	height: 44px;
	overflow: hidden;
	box-sizing: border-box;
	display: flex;
	justify-content: flex-end;
	align-items: flex-end;
}
.subSection2 {
	margin-bottom: 13px;
	margin-right: 6px;
	display: flex;
	justify-content: center;
	align-items: center;
	width: 9.50px;
	height: 17px;
}
.image4 {
	width: 9.50px;
	height: 17px;
}
.text2 {
	mix-blend-mode: normal;
	opacity: 0.9;
	font-family: 'PingFang SC';
	font-size: 17px;
	white-space: nowrap;
	text-align: center;
	color: rgba(0,0,0,1);
	line-height: 23.80px;
	font-weight: 500;
}
.wrap {
	width: 375px;
	height: 64px;
	box-sizing: border-box;
	background: rgba(255,255,255,1);
	display: flex;
	flex-direction: column;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	padding-top: 12px;
	padding-right: 16px;
	padding-bottom: 12px;
	padding-left: 16px;
}
.container {
	width: 343px;
	height: 40px;
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
}
.content {
	width: 343px;
	height: 40px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.main {
	width: 40px;
	height: 40px;
	border-radius: NaNpx;
	box-sizing: border-box;
	background: rgba(242,242,245,1);
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: flex-end;
	align-items: center;
	padding-left: 16px;
}
.image {
	width: 24px;
	height: 24px;
	overflow: hidden;
	box-sizing: border-box;
}
.main1 {
	width: 303px;
	height: 40px;
	overflow: hidden;
	border-radius: NaNpx;
	box-sizing: border-box;
	background: rgba(242,242,245,1);
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: flex-start;
	align-items: center;
	padding-right: 16px;
	padding-left: 8px;
}
.text {
	margin-left: 8px;
	font-family: 'PingFang SC';
	font-size: 16px;
	white-space: nowrap;
	color: rgba(144,147,153,1);
	line-height: 24px;
	font-weight: 400;
}
.wrap2 {
	width: 375px;
	height: 640px;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 12px;
	padding-top: 16px;
	padding-right: 16px;
	padding-bottom: 16px;
	padding-left: 16px;
}
.container2 {
	width: 343px;
	height: 608px;
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	align-items: center;
}
.content3 {
	width: 343px;
	height: 268px;
	border-radius: 8px;
	box-sizing: border-box;
	background: rgba(255,255,255,1);
	display: flex;
	flex-direction: column;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 12px;
	padding-top: 12px;
	padding-right: 12px;
	padding-bottom: 12px;
	padding-left: 12px;
}
.main4 {
	width: 319px;
	height: 244px;
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	align-items: center;
}
.text3 {
	width: 319px;
	font-family: 'PingFang SC';
	font-size: 20px;
	white-space: nowrap;
	color: rgba(48,49,51,1);
	line-height: 28px;
	font-weight: 500;
}
.section3 {
	width: 319px;
	height: 24px;
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 8px;
	margin-top: 12px;
}
.subSection3 {
	width: 319px;
	height: 24px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.text4 {
	font-family: 'PingFang SC';
	font-size: 16px;
	white-space: nowrap;
	color: rgba(96,98,102,1);
	line-height: 24px;
	font-weight: 400;
}
.text5 {
	width: 247px;
	font-family: 'PingFang SC';
	font-size: 16px;
	white-space: nowrap;
	color: rgba(48,49,51,1);
	line-height: 24px;
	font-weight: 500;
}
.section4 {
	width: 319px;
	height: 24px;
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 8px;
	margin-top: 12px;
}
.subSection4 {
	width: 319px;
	height: 24px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.text6 {
	font-family: 'PingFang SC';
	font-size: 16px;
	white-space: nowrap;
	color: rgba(96,98,102,1);
	line-height: 24px;
	font-weight: 400;
}
.text7 {
	width: 247px;
	font-family: 'PingFang SC';
	font-size: 16px;
	white-space: nowrap;
	color: rgba(48,49,51,1);
	line-height: 24px;
	font-weight: 500;
}
.section5 {
	width: 319px;
	height: 84px;
	border-radius: 4px;
	box-sizing: border-box;
	background: rgba(242,243,245,1);
	display: flex;
	flex-direction: column;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 4px;
	margin-top: 12px;
}
.subSection5 {
	width: 303px;
	height: 68px;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	align-items: center;
}
.text8 {
	width: 303px;
	font-family: 'PingFang SC';
	font-size: 14px;
	white-space: nowrap;
	color: rgba(48,49,51,1);
	line-height: 24px;
	font-weight: 500;
}
.text9 {
	width: 303px;
	font-family: 'PingFang SC';
	font-size: 14px;
	color: rgba(96,98,102,1);
	line-height: 20px;
	font-weight: 400;
}
.section6 {
	width: 319px;
	height: 36px;
	border-radius: 8px;
	box-sizing: border-box;
	background: rgba(43,186,66,1);
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 10px;
	margin-top: 12px;
}
.text10 {
	font-family: 'PingFang SC';
	font-size: 14px;
	white-space: nowrap;
	color: rgba(255,255,255,1);
	line-height: 14px;
	font-weight: 500;
}
.content4 {
	width: 343px;
	height: 292px;
	border-radius: 8px;
	box-sizing: border-box;
	background: rgba(255,255,255,1);
	display: flex;
	flex-direction: column;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 12px;
	padding-top: 12px;
	padding-right: 12px;
	padding-bottom: 12px;
	padding-left: 12px;
	margin-top: 12px;
}
.main5 {
	width: 319px;
	height: 268px;
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	align-items: center;
}
.text11 {
	width: 319px;
	font-family: 'PingFang SC';
	font-size: 20px;
	white-space: nowrap;
	color: rgba(48,49,51,1);
	line-height: 28px;
	font-weight: 500;
}
.section7 {
	width: 319px;
	height: 24px;
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 8px;
	margin-top: 12px;
}
.subSection6 {
	width: 319px;
	height: 24px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.text12 {
	font-family: 'PingFang SC';
	font-size: 16px;
	white-space: nowrap;
	color: rgba(96,98,102,1);
	line-height: 24px;
	font-weight: 400;
}
.text13 {
	width: 247px;
	font-family: 'PingFang SC';
	font-size: 16px;
	white-space: nowrap;
	color: rgba(48,49,51,1);
	line-height: 24px;
	font-weight: 500;
}
.section8 {
	width: 319px;
	height: 48px;
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 8px;
	margin-top: 12px;
}
.subSection7 {
	width: 319px;
	height: 48px;
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
}
.text14 {
	font-family: 'PingFang SC';
	font-size: 16px;
	white-space: nowrap;
	color: rgba(96,98,102,1);
	line-height: 24px;
	font-weight: 400;
}
.text15 {
	width: 247px;
	font-family: 'PingFang SC';
	font-size: 16px;
	color: rgba(48,49,51,1);
	line-height: 24px;
	font-weight: 500;
}
.section9 {
	width: 319px;
	height: 84px;
	border-radius: 4px;
	box-sizing: border-box;
	background: rgba(243,245,246,1);
	display: flex;
	flex-direction: column;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 4px;
	margin-top: 12px;
}
.subSection8 {
	width: 303px;
	height: 68px;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	align-items: center;
}
.text16 {
	width: 303px;
	font-family: 'PingFang SC';
	font-size: 14px;
	white-space: nowrap;
	color: rgba(48,49,51,1);
	line-height: 24px;
	font-weight: 500;
}
.text17 {
	width: 303px;
	font-family: 'PingFang SC';
	font-size: 14px;
	color: rgba(96,98,102,1);
	line-height: 20px;
	font-weight: 400;
}
.section10 {
	width: 319px;
	height: 36px;
	border-radius: 8px;
	box-sizing: border-box;
	background: rgba(43,186,66,1);
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 10px;
	margin-top: 12px;
}
.text18 {
	font-family: 'PingFang SC';
	font-size: 14px;
	white-space: nowrap;
	color: rgba(255,255,255,1);
	line-height: 14px;
	font-weight: 500;
}
.text19 {
	margin-top: 12px;
	width: 343px;
	font-family: 'PingFang SC';
	font-size: 14px;
	white-space: nowrap;
	text-align: center;
	color: rgba(144,147,153,1);
	line-height: 24px;
	font-weight: 400;
}
</style>
