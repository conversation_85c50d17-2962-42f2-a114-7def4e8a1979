<template>
  <div class="page">
    <div class="wrap">
      <div class="container">
        <div class="content">
          <div class="main">
            <div class="section">
              <div class="subSection"></div>
              <p class="text"><span class="text__seg0">9:41</span></p>
            </div>
            <div class="section1">
              <div class="subSection1">
                <img
                  :src=" require('./assets/Image1.png')"
                  class="image"
                  alt="Image Asset 1"
                /><img
                  :src=" require('./assets/Image2.png')"
                  class="image1"
                  alt="Image Asset 2"
                />
                <div class="block">
                  <div class="subBlock">
                    <div class="div"></div>
                    <img
                      :src=" require('./assets/Image3.png')"
                      class="image2"
                      alt="Image Asset 3"
                    />
                  </div>
                  <div class="subBlock1"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="content1">
          <div class="main1">
            <div class="section2">
              <div class="subSection2">
                <img
                  :src=" require('./assets/Image4.png')"
                  class="image3"
                  alt="Image Asset 4"
                />
              </div>
            </div>
            <p class="text1">会议详情</p>
          </div>
        </div>
      </div>
    </div>
    <div class="wrap2">
      <div class="container2">
        <p class="text26">紧急技术评审紧急技术评审紧急技术评审紧急技术评审</p>
        <div class="content3">
          <div class="main7">
            <p class="text27">开始时间</p>
            <p class="text28">2025-06-30&nbsp;18:58:51</p>
          </div>
        </div>
        <div class="content4">
          <div class="main8">
            <p class="text29">会议地点</p>
            <p class="text30">成都市农业农村局1006会议室</p>
          </div>
        </div>
        <div class="content5">
          <div class="main9">
            <p class="text31">主持人</p>
            <p class="text32">张三</p>
          </div>
        </div>
        <div class="content6">
          <div class="main10">
            <p class="text33">与会人员</p>
            <div class="section8">
              <div class="subSection13">
                <div class="block10"><p class="text34">陈宇航</p></div>
                <div class="block11"><p class="text35">宋一</p></div>
                <div class="block12"><p class="text36">王若琳</p></div>
                <div class="block13"><p class="text37">孙语桐</p></div>
              </div>
              <div class="subSection14">
                <div class="block14"><p class="text38">周墨</p></div>
                <div class="block15"><p class="text39">吴欣妍</p></div>
                <div class="block16"><p class="text40">徐浩然</p></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="wrap3">
      <div class="container3">
        <p class="text41">会议议题信息</p>
        <div class="content7">
          <div class="main11">
            <img
              :src=" require('./assets/Image5.png')"
              class="image12"
              alt="Image Asset 5"
            />
            <p class="text42">议题导出</p>
          </div>
        </div>
      </div>
    </div>
    <div class="wrap1">
      <div class="container1">
        <div class="content2">
          <div class="main2">
            <div class="section3">
              <div class="subSection3">
                <div class="block1">
                  <div class="subBlock2"><p class="text2">1</p></div>
                  <p class="text3">会议开场</p>
                </div>
              </div>
              <div class="subSection4">
                <img
                  :src=" require('./assets/Image6.png')"
                  class="image4"
                  alt="Image Asset 6"
                />
              </div>
            </div>
          </div>
          <div class="main3">
            <div class="section4">
              <div class="subSection5">
                <div class="block2">
                  <div class="subBlock3"><p class="text4">2</p></div>
                  <p class="text5">产品需求讨论</p>
                </div>
              </div>
              <div class="subSection6">
                <div class="block3">
                  <img
                    :src=" require('./assets/Image7.png')"
                    class="image5"
                    alt="Image Asset 7"
                  />
                  <div class="subBlock4">
                    <div class="div1">
                      <p class="text6">附件：</p>
                      <div class="div2">
                        <div class="div3">
                          <div class="div4">
                            <div class="div5">
                              <img
                                :src=" require('./assets/Image8.png')"
                                class="image6"
                                alt="Image Asset 8"
                              />
                              <p class="text7">产品需求文档.pdf</p>
                            </div>
                          </div>
                          <div class="div6">
                            <div class="div7">
                              <div class="div8"><p class="text8">预览</p></div>
                              <div class="div9"><p class="text9">下载</p></div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="div10">
                        <div class="div11">
                          <div class="div12">
                            <div class="div13">
                              <img
                                :src=" require('./assets/Image9.png')"
                                class="image7"
                                alt="Image Asset 9"
                              />
                              <p class="text10">
                                用户调研数据用户调研数据.xlsx
                              </p>
                            </div>
                          </div>
                          <div class="div14">
                            <div class="div15">
                              <div class="div16">
                                <p class="text11">预览</p>
                              </div>
                              <div class="div17">
                                <p class="text12">下载</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="main4">
            <div class="section5">
              <div class="subSection7">
                <div class="block4">
                  <div class="subBlock5"><p class="text13">3</p></div>
                  <p class="text14">技术方案评审</p>
                </div>
              </div>
              <div class="subSection8">
                <div class="block5">
                  <img
                    :src=" require('./assets/Image10.png')"
                    class="image8"
                    alt="Image Asset 10"
                  />
                  <div class="subBlock6">
                    <div class="div18">
                      <p class="text15">附件：</p>
                      <div class="div19">
                        <div class="div20">
                          <div class="div21">
                            <div class="div22">
                              <img
                                :src=" require('./assets/Image11.png')"
                                class="image9"
                                alt="Image Asset 11"
                              />
                              <p class="text16">技术方案设计.docx</p>
                            </div>
                          </div>
                          <div class="div23">
                            <div class="div24">
                              <div class="div25">
                                <p class="text17">预览</p>
                              </div>
                              <div class="div26">
                                <p class="text18">下载</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="div27">
                        <div class="div28">
                          <div class="div29">
                            <div class="div30">
                              <img
                                :src=" require('./assets/Image12.png')"
                                class="image10"
                                alt="Image Asset 12"
                              />
                              <p class="text19">系统架构图.png</p>
                            </div>
                          </div>
                          <div class="div31">
                            <div class="div32">
                              <div class="div33">
                                <p class="text20">预览</p>
                              </div>
                              <div class="div34">
                                <p class="text21">下载</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="main5">
            <div class="section6">
              <div class="subSection9">
                <div class="block6">
                  <div class="subBlock7"><p class="text22">4</p></div>
                  <p class="text23">时间计划制定</p>
                </div>
              </div>
              <div class="subSection10">
                <div class="block7">
                  <img
                    :src=" require('./assets/Image13.png')"
                    class="image11"
                    alt="Image Asset 13"
                  />
                  <div class="subBlock8"></div>
                </div>
              </div>
            </div>
          </div>
          <div class="main6">
            <div class="section7">
              <div class="subSection11">
                <div class="block8">
                  <div class="subBlock9"><p class="text24">5</p></div>
                  <p class="text25">会议总结</p>
                </div>
              </div>
              <div class="subSection12">
                <div class="block9">
                  <div class="subBlock10"></div>
                  <div class="subBlock11"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'GeneratedComponent',

  props: {

  },
  setup(props) {


    return {


    };
  },
};
</script>

<style scoped>
.page {
	width: 375px;
	height: 994px;
	overflow: hidden;
	box-sizing: border-box;
	background: rgba(243,245,246,1);
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	align-items: flex-start;
}
.wrap {
	width: 375px;
	height: 88px;
	box-sizing: border-box;
	background: rgba(255,255,255,1);
	display: flex;
	flex-direction: column;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
}
.container {
	width: 375px;
	height: 88px;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	align-items: center;
}
.content {
	width: 375px;
	height: 44px;
	overflow: hidden;
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 250px;
	padding-right: 14px;
	padding-left: 14px;
}
.main {
	width: 347px;
	height: 19px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.section {
	width: 59.62px;
	height: 19px;
	overflow: hidden;
	box-sizing: border-box;
	position: relative;
}
.subSection {
	width: 59.62px;
	box-sizing: border-box;
	min-height: 19px;
	position: absolute;
	top: 0px;
	left: 0px;
}
.text {
	position: absolute;
	top: 0.33px;
	left: 0px;
	width: 59.62px;
	font-family: 'PingFang SC';
	font-size: 15px;
	white-space: nowrap;
	color: rgba(0,0,0,1);
	line-height: 21px;
	letter-spacing: -0.30px;
	font-weight: 500;
}
.text__seg0 {
	letter-spacing: -0.30px;
}
.section1 {
	width: 67.33px;
	height: 12px;
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 5px;
}
.subSection1 {
	width: 67.33px;
	height: 12px;
	display: flex;
	justify-content: flex-start;
	align-items: flex-start;
}
.image {
	width: 17px;
	height: 10.67px;
	margin-top: 0.67px;
}
.image1 {
	width: 15.33px;
	height: 11px;
	margin-top: 0.50px;
	margin-left: 5px;
}
.block {
	margin-left: 5px;
	position: relative;
	width: 25px;
	height: 12px;
}
.subBlock {
	width: 25px;
	height: 12px;
	box-sizing: border-box;
	position: absolute;
	top: 0px;
	left: 0px;
	display: flex;
	justify-content: flex-start;
	align-items: flex-start;
}
.div {
	width: 18px;
	background-color: rgba(0,0,0,1);
	border-radius: 1.33px;
	box-sizing: border-box;
	background: rgba(0,0,0,1);
	min-height: 7.33px;
	margin-top: 2.33px;
	margin-left: 2.33px;
}
.image2 {
	width: 1.33px;
	height: 4px;
	margin-top: 4px;
	margin-left: 3px;
}
.subBlock1 {
	width: 22px;
	mix-blend-mode: normal;
	opacity: 0.35;
	border-radius: 2.67px;
	border-color: rgba(0,0,0,1);
	border-style: solid;
	border-width: 1px;
	box-sizing: border-box;
	min-height: 11.33px;
	position: absolute;
	top: 0.33px;
	left: 0.33px;
}
.content1 {
	width: 375px;
	height: 44px;
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: flex-start;
	align-items: center;
	gap: 114px;
}
.main1 {
	width: 221.50px;
	height: 44px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.section2 {
	width: 40px;
	height: 44px;
	overflow: hidden;
	box-sizing: border-box;
	display: flex;
	justify-content: flex-end;
	align-items: flex-end;
}
.subSection2 {
	margin-bottom: 13px;
	margin-right: 6px;
	display: flex;
	justify-content: center;
	align-items: center;
	width: 9.50px;
	height: 17px;
}
.image3 {
	width: 9.50px;
	height: 17px;
}
.text1 {
	mix-blend-mode: normal;
	opacity: 0.9;
	font-family: 'PingFang SC';
	font-size: 17px;
	white-space: nowrap;
	text-align: center;
	color: rgba(0,0,0,1);
	line-height: 23.80px;
	font-weight: 500;
}
.wrap2 {
	width: 375px;
	height: 246px;
	box-sizing: border-box;
	background: rgba(255,255,255,1);
	display: flex;
	flex-direction: column;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 12px;
	padding-top: 16px;
	padding-right: 16px;
	padding-bottom: 16px;
	padding-left: 16px;
}
.container2 {
	width: 343px;
	height: 214px;
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	align-items: center;
}
.text26 {
	width: 343px;
	font-family: 'PingFang SC';
	font-size: 18px;
	color: rgba(48,49,51,1);
	line-height: 25.20px;
	font-weight: 500;
}
.content3 {
	width: 343px;
	height: 20px;
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 8px;
	margin-top: 12px;
}
.main7 {
	width: 343px;
	height: 20px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.text27 {
	width: 64px;
	font-family: 'PingFang SC';
	font-size: 14px;
	white-space: nowrap;
	color: rgba(96,98,102,1);
	line-height: 20px;
	font-weight: 400;
}
.text28 {
	width: 271px;
	font-family: 'PingFang SC';
	font-size: 14px;
	white-space: nowrap;
	color: rgba(48,49,51,1);
	line-height: 20px;
	font-weight: 500;
}
.content4 {
	width: 343px;
	height: 20px;
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 8px;
	margin-top: 12px;
}
.main8 {
	width: 343px;
	height: 20px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.text29 {
	width: 64px;
	font-family: 'PingFang SC';
	font-size: 14px;
	white-space: nowrap;
	color: rgba(96,98,102,1);
	line-height: 20px;
	font-weight: 400;
}
.text30 {
	width: 271px;
	font-family: 'PingFang SC';
	font-size: 14px;
	white-space: nowrap;
	color: rgba(48,49,51,1);
	line-height: 20px;
	font-weight: 500;
}
.content5 {
	width: 343px;
	height: 20px;
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 8px;
	margin-top: 12px;
}
.main9 {
	width: 343px;
	height: 20px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.text31 {
	width: 64px;
	font-family: 'PingFang SC';
	font-size: 14px;
	white-space: nowrap;
	color: rgba(96,98,102,1);
	line-height: 20px;
	font-weight: 400;
}
.text32 {
	width: 271px;
	font-family: 'PingFang SC';
	font-size: 14px;
	white-space: nowrap;
	color: rgba(48,49,51,1);
	line-height: 20px;
	font-weight: 500;
}
.content6 {
	width: 343px;
	height: 56px;
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 8px;
	margin-top: 12px;
}
.main10 {
	width: 343px;
	height: 56px;
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
}
.text33 {
	width: 64px;
	font-family: 'PingFang SC';
	font-size: 14px;
	white-space: nowrap;
	color: rgba(96,98,102,1);
	line-height: 24px;
	font-weight: 400;
}
.section8 {
	width: 271px;
	height: 56px;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	flex-wrap: wrap;
	justify-content: space-between;
	align-items: flex-start;
	gap: 4px;
}
.subSection13 {
	width: 262px;
	height: 26px;
	display: flex;
	justify-content: flex-start;
	align-items: center;
}
.block10 {
	width: 66px;
	height: 26px;
	border-radius: 2px;
	box-sizing: border-box;
	background: rgba(242,243,245,1);
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 10px;
}
.text34 {
	font-family: 'PingFang SC';
	font-size: 14px;
	white-space: nowrap;
	text-align: center;
	color: rgba(48,49,51,1);
	line-height: 24px;
	font-weight: 400;
}
.block11 {
	width: 52px;
	height: 26px;
	border-radius: 2px;
	box-sizing: border-box;
	background: rgba(242,243,245,1);
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 10px;
	margin-left: 4px;
}
.text35 {
	font-family: 'PingFang SC';
	font-size: 14px;
	white-space: nowrap;
	text-align: center;
	color: rgba(48,49,51,1);
	line-height: 24px;
	font-weight: 400;
}
.block12 {
	width: 66px;
	height: 26px;
	border-radius: 2px;
	box-sizing: border-box;
	background: rgba(242,243,245,1);
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 10px;
	margin-left: 4px;
}
.text36 {
	font-family: 'PingFang SC';
	font-size: 14px;
	white-space: nowrap;
	text-align: center;
	color: rgba(48,49,51,1);
	line-height: 24px;
	font-weight: 400;
}
.block13 {
	width: 66px;
	height: 26px;
	border-radius: 2px;
	box-sizing: border-box;
	background: rgba(242,243,245,1);
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 10px;
	margin-left: 4px;
}
.text37 {
	font-family: 'PingFang SC';
	font-size: 14px;
	white-space: nowrap;
	text-align: center;
	color: rgba(48,49,51,1);
	line-height: 24px;
	font-weight: 400;
}
.subSection14 {
	width: 192px;
	height: 26px;
	display: flex;
	justify-content: flex-start;
	align-items: center;
}
.block14 {
	width: 52px;
	height: 26px;
	border-radius: 2px;
	box-sizing: border-box;
	background: rgba(242,243,245,1);
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 10px;
}
.text38 {
	font-family: 'PingFang SC';
	font-size: 14px;
	white-space: nowrap;
	text-align: center;
	color: rgba(48,49,51,1);
	line-height: 24px;
	font-weight: 400;
}
.block15 {
	width: 66px;
	height: 26px;
	border-radius: 2px;
	box-sizing: border-box;
	background: rgba(242,243,245,1);
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 10px;
	margin-left: 4px;
}
.text39 {
	font-family: 'PingFang SC';
	font-size: 14px;
	white-space: nowrap;
	text-align: center;
	color: rgba(48,49,51,1);
	line-height: 24px;
	font-weight: 400;
}
.block16 {
	width: 66px;
	height: 26px;
	border-radius: 2px;
	box-sizing: border-box;
	background: rgba(242,243,245,1);
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 10px;
	margin-left: 4px;
}
.text40 {
	font-family: 'PingFang SC';
	font-size: 14px;
	white-space: nowrap;
	text-align: center;
	color: rgba(48,49,51,1);
	line-height: 24px;
	font-weight: 400;
}
.wrap3 {
	width: 374px;
	height: 56px;
	border-color: rgba(220,223,230,1);
	border-style: dashed;
	border-width: NaNpx;
	box-sizing: border-box;
	background: rgba(255,255,255,1);
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 12px;
	padding-right: 16px;
	padding-left: 16px;
	margin-top: 12px;
	margin-left: 1px;
}
.container3 {
	width: 342px;
	height: 32px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.text41 {
	font-family: 'PingFang SC';
	font-size: 18px;
	white-space: nowrap;
	color: rgba(48,49,51,1);
	line-height: 25.20px;
	font-weight: 500;
}
.content7 {
	width: 100px;
	height: 32px;
	border-radius: 4px;
	border-color: rgba(43,186,66,1);
	border-style: solid;
	border-width: 1px;
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 4px;
}
.main11 {
	width: 76px;
	height: 16px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.image12 {
	width: 16px;
	height: 16px;
	overflow: hidden;
	box-sizing: border-box;
}
.text42 {
	font-family: 'PingFang SC';
	font-size: 14px;
	white-space: nowrap;
	color: rgba(43,186,66,1);
	line-height: 14px;
	font-weight: 500;
}
.wrap1 {
	width: 375px;
	height: 588px;
	box-sizing: border-box;
	background: rgba(255,255,255,1);
	display: flex;
	flex-direction: column;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 12px;
	padding-top: 16px;
	padding-right: 16px;
	padding-bottom: 16px;
	padding-left: 16px;
}
.container1 {
	width: 343px;
	height: 556px;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 8px;
}
.content2 {
	width: 343px;
	height: 556px;
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	align-items: center;
}
.main2 {
	width: 343px;
	height: 52px;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 4px;
}
.section3 {
	width: 343px;
	height: 52px;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	align-items: center;
}
.subSection3 {
	width: 343px;
	height: 24px;
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 8px;
}
.block1 {
	width: 343px;
	height: 24px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.subBlock2 {
	width: 24px;
	height: 24px;
	border-radius: 14px;
	box-sizing: border-box;
	background: rgba(43,186,66,1);
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 10px;
}
.text2 {
	font-family: 'PingFang SC';
	font-size: 16px;
	white-space: nowrap;
	text-align: center;
	color: rgba(255,255,255,1);
	line-height: 24px;
	font-weight: 500;
}
.text3 {
	width: 311px;
	font-family: 'PingFang SC';
	font-size: 16px;
	white-space: nowrap;
	color: rgba(48,49,51,1);
	line-height: 24px;
	font-weight: 500;
}
.subSection4 {
	width: 343px;
	height: 24px;
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: flex-start;
	align-items: center;
	gap: 8px;
}
.image4 {
	width: 24px;
	height: 24px;
	box-sizing: border-box;
}
.main3 {
	width: 343px;
	height: 192px;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 4px;
	margin-top: 8px;
}
.section4 {
	width: 343px;
	height: 192px;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	align-items: center;
}
.subSection5 {
	width: 343px;
	height: 24px;
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 8px;
}
.block2 {
	width: 343px;
	height: 24px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.subBlock3 {
	width: 24px;
	height: 24px;
	border-radius: 14px;
	box-sizing: border-box;
	background: rgba(43,186,66,1);
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 10px;
}
.text4 {
	font-family: 'PingFang SC';
	font-size: 16px;
	white-space: nowrap;
	text-align: center;
	color: rgba(255,255,255,1);
	line-height: 24px;
	font-weight: 500;
}
.text5 {
	width: 311px;
	font-family: 'PingFang SC';
	font-size: 16px;
	white-space: nowrap;
	color: rgba(48,49,51,1);
	line-height: 24px;
	font-weight: 500;
}
.subSection6 {
	width: 343px;
	height: 164px;
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 8px;
}
.block3 {
	width: 343px;
	height: 164px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.image5 {
	width: 24px;
	height: 164px;
	box-sizing: border-box;
}
.subBlock4 {
	width: 311px;
	height: 164px;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	flex-wrap: nowrap;
	justify-content: flex-start;
	align-items: center;
	gap: 8px;
	padding-bottom: 12px;
}
.div1 {
	width: 311px;
	height: 152px;
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	align-items: center;
}
.text6 {
	width: 311px;
	font-family: 'PingFang SC';
	font-size: 14px;
	white-space: nowrap;
	color: rgba(144,147,153,1);
	line-height: 24px;
	font-weight: 400;
}
.div2 {
	width: 311px;
	height: 48px;
	border-radius: 4px;
	box-sizing: border-box;
	background: rgba(243,245,246,1);
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 8px;
	padding-top: 12px;
	padding-right: 12px;
	padding-bottom: 12px;
	padding-left: 12px;
	margin-top: 8px;
}
.div3 {
	width: 287px;
	height: 24px;
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
}
.div4 {
	width: 129px;
	height: 20px;
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 4px;
}
.div5 {
	width: 129px;
	height: 20px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.image6 {
	width: 15px;
	height: 15px;
	overflow: hidden;
	box-sizing: border-box;
}
.text7 {
	font-family: 'PingFang SC';
	font-size: 14px;
	white-space: nowrap;
	color: rgba(96,98,102,1);
	line-height: 20px;
	font-weight: 400;
}
.div6 {
	width: 96px;
	height: 24px;
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 8px;
}
.div7 {
	width: 96px;
	height: 24px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.div8 {
	width: 44px;
	height: 24px;
	border-radius: 4px;
	border-color: rgba(255,255,255,1);
	border-style: solid;
	border-width: 1px;
	box-sizing: border-box;
	background: rgba(255,255,255,1);
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 10px;
}
.text8 {
	font-family: 'PingFang SC';
	font-size: 14px;
	white-space: nowrap;
	color: rgba(96,98,102,1);
	line-height: 24px;
	font-weight: 400;
}
.div9 {
	width: 44px;
	height: 24px;
	border-radius: 4px;
	border-color: rgba(255,255,255,1);
	border-style: solid;
	border-width: 1px;
	box-sizing: border-box;
	background: rgba(255,255,255,1);
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 10px;
}
.text9 {
	font-family: 'PingFang SC';
	font-size: 14px;
	white-space: nowrap;
	color: rgba(96,98,102,1);
	line-height: 24px;
	font-weight: 400;
}
.div10 {
	width: 311px;
	height: 64px;
	border-radius: 4px;
	box-sizing: border-box;
	background: rgba(243,245,246,1);
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 8px;
	padding-right: 12px;
	padding-left: 12px;
	margin-top: 8px;
}
.div11 {
	width: 287px;
	height: 40px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.div12 {
	width: 192px;
	height: 40px;
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 4px;
}
.div13 {
	width: 192px;
	height: 40px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.image7 {
	width: 15px;
	height: 15px;
	overflow: hidden;
	box-sizing: border-box;
}
.text10 {
	width: 173px;
	font-family: 'PingFang SC';
	font-size: 14px;
	color: rgba(96,98,102,1);
	line-height: 20px;
	font-weight: 400;
}
.div14 {
	width: 96px;
	height: 24px;
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 8px;
}
.div15 {
	width: 96px;
	height: 24px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.div16 {
	width: 44px;
	height: 24px;
	border-radius: 4px;
	border-color: rgba(255,255,255,1);
	border-style: solid;
	border-width: 1px;
	box-sizing: border-box;
	background: rgba(255,255,255,1);
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 10px;
}
.text11 {
	font-family: 'PingFang SC';
	font-size: 14px;
	white-space: nowrap;
	color: rgba(96,98,102,1);
	line-height: 24px;
	font-weight: 400;
}
.div17 {
	width: 44px;
	height: 24px;
	border-radius: 4px;
	border-color: rgba(255,255,255,1);
	border-style: solid;
	border-width: 1px;
	box-sizing: border-box;
	background: rgba(255,255,255,1);
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 10px;
}
.text12 {
	font-family: 'PingFang SC';
	font-size: 14px;
	white-space: nowrap;
	color: rgba(96,98,102,1);
	line-height: 24px;
	font-weight: 400;
}
.main4 {
	width: 343px;
	height: 176px;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 4px;
	margin-top: 8px;
}
.section5 {
	width: 343px;
	height: 176px;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	align-items: center;
}
.subSection7 {
	width: 343px;
	height: 24px;
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 8px;
}
.block4 {
	width: 343px;
	height: 24px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.subBlock5 {
	width: 24px;
	height: 24px;
	border-radius: 14px;
	box-sizing: border-box;
	background: rgba(43,186,66,1);
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 10px;
}
.text13 {
	font-family: 'PingFang SC';
	font-size: 16px;
	white-space: nowrap;
	text-align: center;
	color: rgba(255,255,255,1);
	line-height: 24px;
	font-weight: 500;
}
.text14 {
	width: 311px;
	font-family: 'PingFang SC';
	font-size: 16px;
	white-space: nowrap;
	color: rgba(48,49,51,1);
	line-height: 24px;
	font-weight: 500;
}
.subSection8 {
	width: 343px;
	height: 148px;
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 8px;
}
.block5 {
	width: 343px;
	height: 148px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.image8 {
	width: 24px;
	height: 148px;
	box-sizing: border-box;
}
.subBlock6 {
	width: 311px;
	height: 148px;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	flex-wrap: nowrap;
	justify-content: flex-start;
	align-items: center;
	gap: 8px;
	padding-bottom: 12px;
}
.div18 {
	width: 311px;
	height: 136px;
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	align-items: center;
}
.text15 {
	width: 311px;
	font-family: 'PingFang SC';
	font-size: 14px;
	white-space: nowrap;
	color: rgba(144,147,153,1);
	line-height: 24px;
	font-weight: 400;
}
.div19 {
	width: 311px;
	height: 48px;
	border-radius: 4px;
	box-sizing: border-box;
	background: rgba(243,245,246,1);
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 8px;
	padding-top: 12px;
	padding-right: 12px;
	padding-bottom: 12px;
	padding-left: 12px;
	margin-top: 8px;
}
.div20 {
	width: 287px;
	height: 24px;
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
}
.div21 {
	width: 138px;
	height: 20px;
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 4px;
}
.div22 {
	width: 138px;
	height: 20px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.image9 {
	width: 15px;
	height: 15px;
	overflow: hidden;
	box-sizing: border-box;
}
.text16 {
	font-family: 'PingFang SC';
	font-size: 14px;
	white-space: nowrap;
	color: rgba(96,98,102,1);
	line-height: 20px;
	font-weight: 400;
}
.div23 {
	width: 96px;
	height: 24px;
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 8px;
}
.div24 {
	width: 96px;
	height: 24px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.div25 {
	width: 44px;
	height: 24px;
	border-radius: 4px;
	border-color: rgba(255,255,255,1);
	border-style: solid;
	border-width: 1px;
	box-sizing: border-box;
	background: rgba(255,255,255,1);
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 10px;
}
.text17 {
	font-family: 'PingFang SC';
	font-size: 14px;
	white-space: nowrap;
	color: rgba(96,98,102,1);
	line-height: 24px;
	font-weight: 400;
}
.div26 {
	width: 44px;
	height: 24px;
	border-radius: 4px;
	border-color: rgba(255,255,255,1);
	border-style: solid;
	border-width: 1px;
	box-sizing: border-box;
	background: rgba(255,255,255,1);
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 10px;
}
.text18 {
	font-family: 'PingFang SC';
	font-size: 14px;
	white-space: nowrap;
	color: rgba(96,98,102,1);
	line-height: 24px;
	font-weight: 400;
}
.div27 {
	width: 311px;
	height: 48px;
	border-radius: 4px;
	box-sizing: border-box;
	background: rgba(243,245,246,1);
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 8px;
	padding-top: 12px;
	padding-right: 12px;
	padding-bottom: 12px;
	padding-left: 12px;
	margin-top: 8px;
}
.div28 {
	width: 287px;
	height: 24px;
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
}
.div29 {
	width: 117px;
	height: 20px;
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 4px;
}
.div30 {
	width: 117px;
	height: 20px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.image10 {
	width: 15px;
	height: 15px;
	overflow: hidden;
	box-sizing: border-box;
}
.text19 {
	font-family: 'PingFang SC';
	font-size: 14px;
	white-space: nowrap;
	color: rgba(96,98,102,1);
	line-height: 20px;
	font-weight: 400;
}
.div31 {
	width: 96px;
	height: 24px;
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 8px;
}
.div32 {
	width: 96px;
	height: 24px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.div33 {
	width: 44px;
	height: 24px;
	border-radius: 4px;
	border-color: rgba(255,255,255,1);
	border-style: solid;
	border-width: 1px;
	box-sizing: border-box;
	background: rgba(255,255,255,1);
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 10px;
}
.text20 {
	font-family: 'PingFang SC';
	font-size: 14px;
	white-space: nowrap;
	color: rgba(96,98,102,1);
	line-height: 24px;
	font-weight: 400;
}
.div34 {
	width: 44px;
	height: 24px;
	border-radius: 4px;
	border-color: rgba(255,255,255,1);
	border-style: solid;
	border-width: 1px;
	box-sizing: border-box;
	background: rgba(255,255,255,1);
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 10px;
}
.text21 {
	font-family: 'PingFang SC';
	font-size: 14px;
	white-space: nowrap;
	color: rgba(96,98,102,1);
	line-height: 24px;
	font-weight: 400;
}
.main5 {
	width: 343px;
	height: 52px;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 4px;
	margin-top: 8px;
}
.section6 {
	width: 343px;
	height: 52px;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	align-items: center;
}
.subSection9 {
	width: 343px;
	height: 24px;
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 8px;
}
.block6 {
	width: 343px;
	height: 24px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.subBlock7 {
	width: 24px;
	height: 24px;
	border-radius: 14px;
	box-sizing: border-box;
	background: rgba(43,186,66,1);
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 10px;
}
.text22 {
	font-family: 'PingFang SC';
	font-size: 16px;
	white-space: nowrap;
	text-align: center;
	color: rgba(255,255,255,1);
	line-height: 24px;
	font-weight: 500;
}
.text23 {
	width: 311px;
	font-family: 'PingFang SC';
	font-size: 16px;
	white-space: nowrap;
	color: rgba(48,49,51,1);
	line-height: 24px;
	font-weight: 500;
}
.subSection10 {
	width: 343px;
	height: 24px;
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 8px;
}
.block7 {
	width: 343px;
	height: 24px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.image11 {
	width: 24px;
	height: 24px;
	box-sizing: border-box;
}
.subBlock8 {
	width: 311px;
	box-sizing: border-box;
	min-height: 24px;
	display: flex;
	flex-direction: column;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: flex-start;
	gap: 8px;
}
.main6 {
	width: 343px;
	height: 52px;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 4px;
	margin-top: 8px;
}
.section7 {
	width: 343px;
	height: 52px;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	align-items: center;
}
.subSection11 {
	width: 343px;
	height: 24px;
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 8px;
}
.block8 {
	width: 343px;
	height: 24px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.subBlock9 {
	width: 24px;
	height: 24px;
	border-radius: 14px;
	box-sizing: border-box;
	background: rgba(43,186,66,1);
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 10px;
}
.text24 {
	font-family: 'PingFang SC';
	font-size: 16px;
	white-space: nowrap;
	text-align: center;
	color: rgba(255,255,255,1);
	line-height: 24px;
	font-weight: 500;
}
.text25 {
	width: 311px;
	font-family: 'PingFang SC';
	font-size: 16px;
	white-space: nowrap;
	color: rgba(48,49,51,1);
	line-height: 24px;
	font-weight: 500;
}
.subSection12 {
	width: 343px;
	height: 24px;
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 8px;
}
.block9 {
	width: 343px;
	height: 24px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.subBlock10 {
	width: 24px;
	box-sizing: border-box;
	min-height: 24px;
	display: flex;
	height: 24px;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: center;
	gap: 10px;
}
.subBlock11 {
	width: 311px;
	box-sizing: border-box;
	min-height: 24px;
	display: flex;
	flex-direction: column;
	flex-wrap: nowrap;
	justify-content: center;
	align-items: flex-start;
	gap: 8px;
}
</style>
