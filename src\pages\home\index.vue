<template>
  <div class="meeting-detail-page">
    <!-- 会议基本信息 -->
    <div class="meeting-info-section">
      <VanCell>
        <template #title>
          <div class="meeting-title">{{ meetingInfo.title }}</div>
        </template>
      </VanCell>

      <VanCellGroup>
        <VanCell title="开始时间" :value="meetingInfo.startTime" />
        <VanCell title="会议地点" :value="meetingInfo.location" />
        <VanCell title="主持人" :value="meetingInfo.host" />
        <VanCell title="与会人员">
          <template #value>
            <div class="attendees-list">
              <VanTag
                v-for="attendee in meetingInfo.attendees"
                :key="attendee"
                type="default"
                size="medium"
                class="attendee-tag"
              >
                {{ attendee }}
              </VanTag>
            </div>
          </template>
        </VanCell>
      </VanCellGroup>
    </div>

    <!-- 会议议题信息 -->
    <div class="agenda-section">
      <div class="section-header">
        <span class="section-title">会议议题信息</span>
        <VanButton
          type="primary"
          size="small"
          plain
          @click="exportAgenda"
        >
          <VanIcon name="down" />
          议题导出
        </VanButton>
      </div>
    </div>
    <!-- 议题列表 -->
    <div class="agenda-list-section">
      <VanSteps :active="currentStep" direction="vertical">
        <VanStep
          v-for="(agenda, index) in agendaList"
          :key="index"
        >
          <template #title>
            <div class="agenda-title">{{ agenda.title }}</div>
          </template>

          <template #desc>
            <div v-if="agenda.attachments && agenda.attachments.length > 0" class="agenda-attachments">
              <div class="attachment-label">附件：</div>
              <div class="attachment-list">
                <div
                  v-for="attachment in agenda.attachments"
                  :key="attachment.id"
                  class="attachment-item"
                >
                  <VanCell>
                    <template #icon>
                      <VanIcon :name="getFileIcon(attachment.type)" />
                    </template>
                    <template #title>
                      <span class="attachment-name">{{ attachment.name }}</span>
                    </template>
                    <template #right-icon>
                      <div class="attachment-actions">
                        <VanButton
                          size="mini"
                          type="default"
                          @click="previewFile(attachment)"
                        >
                          预览
                        </VanButton>
                        <VanButton
                          size="mini"
                          type="default"
                          @click="downloadFile(attachment)"
                        >
                          下载
                        </VanButton>
                      </div>
                    </template>
                  </VanCell>
                </div>
              </div>
            </div>
          </template>
        </VanStep>
      </VanSteps>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { showToast } from 'vant'

// 响应式数据
const currentStep = ref(0)
const meetingInfo = ref({})
const agendaList = ref([])

// 模拟会议数据
const mockMeetingInfo = {
  title: '紧急技术评审',
  startTime: '2025-06-30 18:58:51',
  location: '成都市农业农村局1006会议室',
  host: '张三',
  attendees: ['陈宇航', '宋一', '王若琳', '孙语桐', '周墨', '吴欣妍', '徐浩然']
}

const mockAgendaList = [
  {
    title: '会议开场',
    attachments: []
  },
  {
    title: '产品需求讨论',
    attachments: [
      {
        id: 1,
        name: '产品需求文档.pdf',
        type: 'pdf'
      },
      {
        id: 2,
        name: '用户调研数据.xlsx',
        type: 'excel'
      }
    ]
  },
  {
    title: '技术方案评审',
    attachments: [
      {
        id: 3,
        name: '技术方案设计.docx',
        type: 'word'
      },
      {
        id: 4,
        name: '系统架构图.png',
        type: 'image'
      }
    ]
  },
  {
    title: '时间计划制定',
    attachments: []
  },
  {
    title: '会议总结',
    attachments: []
  }
]

// 获取文件图标
const getFileIcon = (type) => {
  const iconMap = {
    pdf: 'description',
    excel: 'description',
    word: 'description',
    image: 'photo'
  }
  return iconMap[type] || 'description'
}

// 导出议题
const exportAgenda = () => {
  showToast('议题导出功能')
}

// 预览文件
const previewFile = (attachment) => {
  showToast(`预览文件：${attachment.name}`)
}

// 下载文件
const downloadFile = (attachment) => {
  showToast(`下载文件：${attachment.name}`)
}

// 初始化数据
onMounted(() => {
  meetingInfo.value = mockMeetingInfo
  agendaList.value = mockAgendaList
  currentStep.value = 2 // 当前进行到第3个议题
})
</script>

<style lang="scss" scoped>
.meeting-detail-page {
  min-height: 100vh;
  background-color: #f7f8fa;

  .meeting-info-section {
    background-color: #fff;
    margin-bottom: 12px;

    .meeting-title {
      font-size: 18px;
      font-weight: 500;
      color: #303133;
      padding: 16px 0 8px;
    }

    :deep(.van-cell) {
      padding: 12px 16px;

      .van-cell__title {
        color: #606266;
        font-size: 14px;
        font-weight: 400;
      }

      .van-cell__value {
        color: #303133;
        font-size: 14px;
        font-weight: 400;
      }
    }

    .attendees-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .attendee-tag {
        background-color: #f2f3f5;
        color: #303133;
        border: none;
        margin: 0;
      }
    }
  }

  .agenda-section {
    background-color: #fff;
    padding: 16px;
    margin-bottom: 12px;
    border-top: 1px dashed #dcdfe6;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .section-title {
        font-size: 18px;
        font-weight: 500;
        color: #303133;
      }

      :deep(.van-button) {
        border-color: #2bba42;
        color: #2bba42;

        .van-icon {
          margin-right: 4px;
        }
      }
    }
  }

  .agenda-list-section {
    background-color: #fff;
    padding: 16px;

    :deep(.van-steps) {
      .van-step__title {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
      }

      .van-step__circle {
        background-color: #2bba42;
        border-color: #2bba42;
      }

      .van-step__line {
        background-color: #ebeef5;
      }

      .van-step--process .van-step__circle {
        background-color: #2bba42;
        border-color: #2bba42;
      }

      .van-step--finish .van-step__circle {
        background-color: #2bba42;
        border-color: #2bba42;
      }
    }

    .agenda-title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
      margin-bottom: 8px;
    }

    .agenda-attachments {
      .attachment-label {
        color: #909399;
        font-size: 14px;
        margin-bottom: 8px;
      }

      .attachment-list {
        .attachment-item {
          background-color: #f3f5f6;
          border-radius: 4px;
          margin-bottom: 8px;

          :deep(.van-cell) {
            background-color: transparent;
            padding: 12px;

            .van-cell__title {
              .attachment-name {
                color: #606266;
                font-size: 14px;
              }
            }

            .attachment-actions {
              display: flex;
              gap: 8px;

              .van-button {
                background-color: #fff;
                border-color: #fff;
                color: #606266;
                font-size: 14px;
                padding: 4px 8px;
                height: 24px;

                &:active {
                  background-color: #f5f5f5;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>

<route lang="yaml">
meta:
  title: 会议详情
name: MeetingDetail
</route>
