<template>
  <div class="home-page">
    <!-- 搜索区域 -->
    <div class="search-section">
      <VanSearch
        v-model="searchKeyword"
        placeholder="输入会议名称查询"
        @search="onSearch"
        @clear="onClear"
      />
    </div>

    <!-- 会议列表 -->
    <div class="meeting-list">
      <VanList
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
      >
        <div
          v-for="meeting in meetingList"
          :key="meeting.id"
          class="meeting-card"
        >
          <VanCard>
            <template #title>
              <div class="meeting-title">
                {{ meeting.title }}
                <VanTag v-if="meeting.urgent" type="danger" size="mini">
                  紧急
                </VanTag>
              </div>
            </template>

            <template #desc>
              <div class="meeting-info">
                <VanCell
                  title="开始时间"
                  :value="meeting.startTime"
                  :border="false"
                />
                <VanCell
                  title="会议地点"
                  :value="meeting.location"
                  :border="false"
                />
                <div class="meeting-agenda">
                  <div class="agenda-title">会议议题</div>
                  <div class="agenda-content">{{ meeting.agenda }}</div>
                </div>
              </div>
            </template>

            <template #footer>
              <VanButton
                type="primary"
                size="small"
                round
                @click="viewDetail(meeting)"
              >
                查看详情
              </VanButton>
            </template>
          </VanCard>
        </div>
      </VanList>
    </div>

    <!-- 空状态 -->
    <VanEmpty
      v-if="!loading && meetingList.length === 0"
      description="暂无会议数据"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'

const router = useRouter()

// 响应式数据
const searchKeyword = ref('')
const loading = ref(false)
const finished = ref(false)
const meetingList = ref([])

// 模拟会议数据
const mockMeetings = [
  {
    id: 1,
    title: '紧急技术评审',
    startTime: '2025-06-30 18:58:51',
    location: '成都市农业农村局1006会议室',
    agenda: '1、关于高标准农田建设的技术方案汇报；2、关于技术方案的头脑风暴讨论；3、关于建设周期的确认',
    urgent: true
  },
  {
    id: 2,
    title: '项目进度汇报',
    startTime: '2025-07-01 09:00:00',
    location: '成都市农业农村局会议室A',
    agenda: '1、项目进度汇报；2、问题讨论；3、下一步计划',
    urgent: false
  }
]

// 搜索功能
const onSearch = (value) => {
  if (!value.trim()) {
    meetingList.value = [...mockMeetings]
    return
  }

  meetingList.value = mockMeetings.filter(meeting =>
    meeting.title.includes(value) ||
    meeting.location.includes(value) ||
    meeting.agenda.includes(value)
  )

  if (meetingList.value.length === 0) {
    showToast('未找到相关会议')
  }
}

// 清空搜索
const onClear = () => {
  searchKeyword.value = ''
  meetingList.value = [...mockMeetings]
}

// 加载更多
const onLoad = () => {
  loading.value = true

  // 模拟异步加载
  setTimeout(() => {
    loading.value = false
    finished.value = true
  }, 1000)
}

// 查看详情
const viewDetail = (meeting) => {
  showToast(`查看会议：${meeting.title}`)
  // 这里可以跳转到详情页
  // router.push(`/meeting/${meeting.id}`)
}

// 初始化数据
onMounted(() => {
  meetingList.value = [...mockMeetings]
})
</script>

<route lang="yaml">
meta:
  title: 首页
name: Home
</route>

<style lang="scss" scoped>
.home-page {
  min-height: 100vh;
  background-color: #f3f5f6;

  .search-section {
    background-color: #fff;
    padding: 12px 16px;
    margin-bottom: 12px;
  }

  .meeting-list {
    padding: 0 16px;

    .meeting-card {
      margin-bottom: 12px;

      :deep(.van-card) {
        background-color: #fff;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .van-card__header {
          padding: 16px 16px 0;
        }

        .van-card__content {
          padding: 12px 16px;
        }

        .van-card__footer {
          padding: 0 16px 16px;
          text-align: right;
        }
      }

      .meeting-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 18px;
        font-weight: 500;
        color: #303133;
        line-height: 24px;
      }

      .meeting-info {
        :deep(.van-cell) {
          padding: 8px 0;

          .van-cell__title {
            color: #606266;
            font-size: 14px;
            font-weight: 400;
            flex: none;
            width: 72px;
          }

          .van-cell__value {
            color: #303133;
            font-size: 14px;
            font-weight: 400;
            text-align: left;
          }
        }

        .meeting-agenda {
          padding: 12px 0 8px;

          .agenda-title {
            color: #606266;
            font-size: 14px;
            font-weight: 400;
            margin-bottom: 8px;
          }

          .agenda-content {
            color: #606266;
            font-size: 14px;
            line-height: 20px;
            background-color: #f2f3f5;
            padding: 12px;
            border-radius: 4px;
          }
        }
      }
    }
  }

  // 空状态样式
  :deep(.van-empty) {
    padding: 60px 0;
  }

  // 列表加载样式
  :deep(.van-list__finished-text) {
    color: #909399;
    font-size: 14px;
    text-align: center;
    padding: 16px 0;
  }

  // 搜索框样式优化
  :deep(.van-search) {
    .van-search__content {
      background-color: #f2f2f5;
      border-radius: 20px;
    }

    .van-field__control {
      font-size: 16px;
    }
  }

  // 标签样式
  :deep(.van-tag--danger) {
    background-color: #ff4757;
    color: #fff;
    border: none;
  }

  // 按钮样式
  :deep(.van-button--primary) {
    background-color: #2bba42;
    border-color: #2bba42;

    &:active {
      background-color: #259c3a;
      border-color: #259c3a;
    }
  }
}
</style>
